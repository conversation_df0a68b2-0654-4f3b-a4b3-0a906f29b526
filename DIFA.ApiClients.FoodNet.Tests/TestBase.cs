using DIFA.ApiClients.FoodNet.Mock;
using WireMock.Server;

namespace DIFA.ApiClients.FoodNet.Tests;

/// <summary>
/// Base class for all test classes to reduce code duplication
/// </summary>
public abstract class TestBase : IDisposable
{
    private readonly WireMockServer _mockServer;
    private readonly HttpClient _httpClient;

    // FoodNet Client for testing
    protected readonly IFoodNetClient Client;

    protected TestBase()
    {
        // Setup WireMock server
        _mockServer = WireMockServer.Start();
        _mockServer.AllowPartialMapping();
        _mockServer.WithMapping(FoodNetMockData.AllMappings);

        // Configure HttpClient to use mock server
        _httpClient = new HttpClient
        {
            BaseAddress = new Uri(_mockServer.Urls[0])
        };

        // Create FoodNetClient that will make HTTP calls to WireMock
        Client = new FoodNetClient(_httpClient);
    }
    
    public void Dispose()
    {
        _mockServer.Stop();
        _httpClient.Dispose();
        GC.SuppressFinalize(this);
    }
}
