using FluentAssertions;
using Xunit;

namespace DIFA.ApiClients.FoodNet.Tests.Services;

public class LCETests : TestBase
{
    [Fact]
    public async Task GetLCEs_WithLCEAndPCEData_ReturnsUnionOfBoth()
    {
        // Act
        var result = await Client.GetLCEsAsync(CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(4);

        result.Should().Contain(lce => lce.ServiceCode == "i9j0k1l2-m3n4-5678-ijkl-901234567890");
        result.Should().Contain(lce => lce.ServiceCode == "j0k1l2m3-n4o5-6789-jklm-012345678901");
        result.Should().Contain(lce => lce.ServiceCode == "k1l2m3n4-o5p6-7890-klmn-123456789012");
        result.Should().Contain(lce => lce.ServiceCode == "l2m3n4o5-p6q7-8901-lmno-234567890123");

        // Verify descriptions
        var lce1 = result.First(lce => lce.ServiceCode == "i9j0k1l2-m3n4-5678-ijkl-901234567890");
        lce1.DescriptionNl.Should().Be("Laboratorium Controle Eenheid 1 NL");
        lce1.DescriptionFr.Should().Be("Unité de Contrôle Laboratoire 1 FR");
    }

    [Fact]
    public async Task GetLCEByServiceCodeAsync_WithValidServiceCode_ReturnsLCE()
    {
        // Act
        var result = await Client.GetLCEByServiceCodeAsync("i9j0k1l2-m3n4-5678-ijkl-901234567890", CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result!.ServiceCode.Should().Be("i9j0k1l2-m3n4-5678-ijkl-901234567890");
        result.DescriptionNl.Should().Be("Laboratorium Controle Eenheid 1 NL");
        result.DescriptionFr.Should().Be("Unité de Contrôle Laboratoire 1 FR");
    }

    [Fact]
    public async Task GetLCEByServiceCodeAsync_WithNonExistentServiceCode_ReturnsNull()
    {
        // Act
        var result = await Client.GetLCEByServiceCodeAsync("NON_EXISTENT", CancellationToken.None);

        // Assert
        result.Should().BeNull();
    }
}
