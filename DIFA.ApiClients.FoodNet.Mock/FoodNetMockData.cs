using Newtonsoft.Json;
using WireMock.Admin.Mappings;

namespace DIFA.ApiClients.FoodNet.Mock;

public static class FoodNetMockData
{
    /// <summary>
    /// Static class containing the mapping data for the FoodNet API mocks.
    /// </summary>
    /// <remarks>
    /// The mappings are loaded from the files in the Mappings folder of the assembly.
    /// </remarks>
    static FoodNetMockData()
    {
        // Get the path to the Mappings folder
        var assembly = typeof(FoodNetMockData).Assembly;
        var rootPath = Path.GetDirectoryName(assembly.Location);

        if (rootPath == null)
        {
            AllMappings = [];
            return;
        }

        var mappingsPath = Path.Combine(rootPath, "Mappings");
        
        if (!Directory.Exists(mappingsPath))
        {
            AllMappings = [];
            return;
        }

        // Temporarily disable JSON loading to test
        AllMappings = [];
    }

    /// <summary>
    /// The mapping data for the FoodNet API mocks.
    /// </summary>
    public static MappingModel[] AllMappings { get; }
}
