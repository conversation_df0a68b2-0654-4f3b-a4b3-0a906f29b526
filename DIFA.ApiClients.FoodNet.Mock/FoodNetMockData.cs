using Newtonsoft.Json;
using WireMock.Admin.Mappings;

namespace DIFA.ApiClients.FoodNet.Mock;

public static class FoodNetMockData
{
    /// <summary>
    /// Static class containing the mapping data for the FoodNet API mocks.
    /// </summary>
    /// <remarks>
    /// The mappings are loaded from the files in the Mappings folder of the assembly.
    /// </remarks>
    static FoodNetMockData()
    {
        // Get the path to the Mappings folder
        var assembly = typeof(FoodNetMockData).Assembly;
        var rootPath = Path.GetDirectoryName(assembly.Location);

        if (rootPath == null)
        {
            AllMappings = [];
            return;
        }

        var mappingsPath = Path.Combine(rootPath, "Mappings");
        
        if (!Directory.Exists(mappingsPath))
        {
            AllMappings = [];
            return;
        }

        var files = Directory.GetFiles(mappingsPath, "*.json", SearchOption.AllDirectories);

        var mappings = new List<MappingModel>();
        foreach (var file in files)
        {
            try
            {
                var content = File.ReadAllText(file);
                var mapping = JsonConvert.DeserializeObject<MappingModel>(content);
                if (mapping != null)
                {
                    mappings.Add(mapping);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to parse JSON file: {file}. Error: {ex.Message}", ex);
            }
        }

        AllMappings = mappings.ToArray();
    }

    /// <summary>
    /// The mapping data for the FoodNet API mocks.
    /// </summary>
    public static MappingModel[] AllMappings { get; }
}
