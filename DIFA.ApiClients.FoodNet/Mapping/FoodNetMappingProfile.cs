using AutoMapper;
using DIFA.ApiClients.FoodNet.Contract;
using DIFA.ApiClients.FoodNet.Entities;

namespace DIFA.ApiClients.FoodNet.Mapping;

/// <summary>
/// AutoMapper profile for FoodNet entity to DTO mappings
/// </summary>
public class FoodNetMappingProfile : Profile
{
    public FoodNetMappingProfile()
    {
        // LCE Entity to LCEDto mapping
        CreateMap<LCE, LCEDto>()
            .ForMember(dest => dest.ServiceCode, opt => opt.MapFrom(src => src.ServiceCode))
            .ForMember(dest => dest.DescriptionNl, opt => opt.MapFrom(src => src.DescriptionNl))
            .ForMember(dest => dest.DescriptionFr, opt => opt.MapFrom(src => src.DescriptionFr));

        // PCE Entity to LCEDto mapping (PCE maps to LCEDto for union operations)
        CreateMap<PCE, LCEDto>()
            .ForMember(dest => dest.ServiceCode, opt => opt.MapFrom(src => src.ServiceCode))
            .ForMember(dest => dest.DescriptionNl, opt => opt.MapFrom(src => src.DescriptionNl))
            .ForMember(dest => dest.DescriptionFr, opt => opt.MapFrom(src => src.DescriptionFr));

        // Parameter Entity to ParameterDto mapping
        CreateMap<Parameter, ParameterDto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.DescriptionNL, opt => opt.MapFrom(src => src.DescriptionNl))
            .ForMember(dest => dest.DescriptionFR, opt => opt.MapFrom(src => src.DescriptionFr));

        // MatrixLevel4 Entity to MatrixDto mapping
        CreateMap<MatrixLevel4, MatrixDto>()
            .ForMember(dest => dest.MatrixId, opt => opt.MapFrom(src => src.MatrixNiv4Id))
            .ForMember(dest => dest.DescriptionNL, opt => opt.MapFrom(src => src.DescriptionNl))
            .ForMember(dest => dest.DescriptionFR, opt => opt.MapFrom(src => src.DescriptionFr))
            .ForMember(dest => dest.Level, opt => opt.MapFrom(src => 4));

        // MatrixLevel5 Entity to MatrixDto mapping
        CreateMap<MatrixLevel5, MatrixDto>()
            .ForMember(dest => dest.MatrixId, opt => opt.MapFrom(src => src.MatrixNiv5Id))
            .ForMember(dest => dest.DescriptionNL, opt => opt.MapFrom(src => src.DescriptionNl))
            .ForMember(dest => dest.DescriptionFR, opt => opt.MapFrom(src => src.DescriptionFr))
            .ForMember(dest => dest.Level, opt => opt.MapFrom(src => 5));

        // Mission Entity to MissionDto mapping
        CreateMap<Mission, MissionDto>()
            .ForMember(dest => dest.MissionId, opt => opt.MapFrom(src => src.MissionId))
            .ForMember(dest => dest.MissionNb, opt => opt.MapFrom(src => src.MissionNb))
            .ForMember(dest => dest.PlannedStartDate, opt => opt.MapFrom(src => src.PlannedStartDateHour))
            .ForMember(dest => dest.PlannedEndDate, opt => opt.MapFrom(src => src.PlannedEndDateHour))
            .ForMember(dest => dest.ActualStartDate, opt => opt.MapFrom(src => src.ActualStartDateHour))
            .ForMember(dest => dest.ActualEndDate, opt => opt.MapFrom(src => src.ActualEndDateHour))
            .ForMember(dest => dest.LCE, opt => opt.MapFrom(src => src.PceCodeOperator))
            .ForMember(dest => dest.LceDto, opt => opt.Ignore()); // This will be set manually in the service

        // Sample Entity to SampleDto mapping
        CreateMap<Sample, SampleDto>()
            .ForMember(dest => dest.SampleId, opt => opt.MapFrom(src => src.SampleId))
            .ForMember(dest => dest.SampleParameterId, opt => opt.MapFrom(src => src.SampleParameterId))
            .ForMember(dest => dest.SampleNb, opt => opt.MapFrom(src => src.SampleNb))
            .ForMember(dest => dest.ControlId, opt => opt.MapFrom(src => src.ControlId))
            .ForMember(dest => dest.SamplingId, opt => opt.MapFrom(src => src.SamplingId))
            .ForMember(dest => dest.ParameterId, opt => opt.MapFrom(src => src.ParameterId))
            .ForMember(dest => dest.ParameterResult, opt => opt.MapFrom(src => src.ParameterResult))
            .ForMember(dest => dest.SampleResult, opt => opt.MapFrom(src => src.SampleResult))
            .ForMember(dest => dest.IsExtra, opt => opt.MapFrom(src => src.IsExtra))
            .ForMember(dest => dest.MissionId, opt => opt.MapFrom(src => src.MissionId))
            .ForMember(dest => dest.Parameter, opt => opt.MapFrom(src => src.Parameter));

        // Question Entity to QuestionDto mapping
        CreateMap<Question, QuestionDto>()
            .ForMember(dest => dest.ItemId, opt => opt.MapFrom(src => src.ItemId))
            .ForMember(dest => dest.ParentChapterTitle, opt => opt.MapFrom(src => src.ParentChapterTitle))
            .ForMember(dest => dest.ParentTitle, opt => opt.MapFrom(src => src.ParentTitle))
            .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.Title))
            .ForMember(dest => dest.QuestionType, opt => opt.MapFrom(src => src.QuestionType))
            .ForMember(dest => dest.QuestionTypeId, opt => opt.MapFrom(src => src.QuestionTypeId))
            .ForMember(dest => dest.TemplateVersionId, opt => opt.MapFrom(src => src.TemplateVersionId));

        // Answer Entity to AnswerDto mapping
        CreateMap<Answer, AnswerDto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.TemplateVersionId, opt => opt.MapFrom(src => src.TemplateVersionId))
            .ForMember(dest => dest.QuestionResultId, opt => opt.MapFrom(src => src.QuestionResultId))
            .ForMember(dest => dest.Result, opt => opt.MapFrom(src => src.Result))
            .ForMember(dest => dest.ScoreId, opt => opt.MapFrom(src => src.ScoreId))
            .ForMember(dest => dest.MissionId, opt => opt.MapFrom(src => src.MissionId));

        // Reverse mappings for create/update operations
        CreateMap<AnswerDto, Answer>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.TemplateVersionId, opt => opt.MapFrom(src => src.TemplateVersionId))
            .ForMember(dest => dest.QuestionResultId, opt => opt.MapFrom(src => src.QuestionResultId))
            .ForMember(dest => dest.Result, opt => opt.MapFrom(src => src.Result))
            .ForMember(dest => dest.ScoreId, opt => opt.MapFrom(src => src.ScoreId))
            .ForMember(dest => dest.MissionId, opt => opt.MapFrom(src => src.MissionId))
            .ForMember(dest => dest.Question, opt => opt.Ignore()); // Navigation property, ignore
    }
}
