using DIFA.ApiClients.FoodNet.Contexts;
using DIFA.ApiClients.FoodNet.Contract;
using Microsoft.EntityFrameworkCore;

namespace DIFA.ApiClients.FoodNet.Services;

public interface ICheckListService
{
    Task<List<QuestionDto>> GetQuestionsAsync(int templateVersionId, CancellationToken cancellationToken);

    Task<List<QuestionWithAnswersDTO>> GetQuestionsWithAnswersAsync(int templateVersionId, string missionId,
        CancellationToken cancellationToken);
}

public class CheckListService : ICheckListService
{
    private readonly DynamoContext _dynamoContext;
    private readonly FoodNetContext _foodNetContext;

    public CheckListService(DynamoContext dynamoContext, FoodNetContext foodNetContext)
    {
        _dynamoContext = dynamoContext;
        _foodNetContext = foodNetContext;
    }

    public async Task<List<QuestionDto>> GetQuestionsAsync(int templateVersionId, CancellationToken cancellationToken)
    {
        var questions = await _dynamoContext.Questions
            .Where(q => q.TemplateVersionId == templateVersionId)
            .Select(q => new QuestionDto
            {
                ItemId = q.ItemId,
                ParentChapterTitle = q.ParentChapterTitle,
                ParentTitle = q.ParentTitle,
                Title = q.Title,
                QuestionType = q.QuestionType,
                QuestionTypeId = q.QuestionTypeId
            })
            .ToListAsync(cancellationToken);

        return questions;
    }

    public async Task<List<QuestionWithAnswersDTO>> GetQuestionsWithAnswersAsync(int templateVersionId,
        string missionId, CancellationToken cancellationToken)
    {
        // Get questions from SQL Server (DynamoContext)
        var questions = await _dynamoContext.Questions
            .Where(q => q.TemplateVersionId == templateVersionId)
            .ToListAsync(cancellationToken);

        // Get answers from Oracle (FoodNetContext)
        var answers = await _foodNetContext.Answers
            .Where(a => a.MissionId == missionId)
            .ToListAsync(cancellationToken);

        // Perform the join in memory
        var questionsWithAnswers = questions.Select(question => new QuestionWithAnswersDTO
        {
            ItemId = question.ItemId,
            ParentChapterTitle = question.ParentChapterTitle,
            ParentTitle = question.ParentTitle,
            Title = question.Title,
            QuestionType = question.QuestionType,
            QuestionTypeId = question.QuestionTypeId,
            Answer = answers.Where(a => a.QuestionResultId == question.ItemId)
                .Select(a => new AnswerDto
                {
                    TemplateVersionId = a.TemplateVersionId,
                    QuestionResultId = a.QuestionResultId,
                    Result = a.Result,
                    ScoreId = a.ScoreId,
                    MissionId = a.MissionId
                }).FirstOrDefault()
        }).ToList();

        return questionsWithAnswers;
    }
}